@echo off
title Mangoo System v2.3.0 - Smart Launcher

REM Mangoo System v2.3.0 - Smart Portable Launcher
REM Developer: Fares Nawaf - 0569329925
REM Email: <EMAIL>
REM Features: Smart trial system, automatic activation on device copy
REM Optimized for silent operation - minimal console output

REM Set portable environment silently
if exist "%~dp0PORTABLE_CONFIG.ini" (
    set "PORTABLE_MODE=1"
    set "APPDATA=%~dp0PortableData"
    set "LOCALAPPDATA=%~dp0PortableData"
    if not exist "%~dp0PortableData" mkdir "%~dp0PortableData" >nul 2>&1

    REM Smart trial system: Check for first run and create desktop shortcut
    REM - On new device: automatically starts new 30-day trial
    REM - After trial expires: shows mandatory activation window
    REM - Activation window can only be closed by activating or closing app
    if not exist "%~dp0PortableData\first_run_completed.flag" (
        call :CreateDesktopShortcut >nul 2>&1
        echo. > "%~dp0PortableData\first_run_completed.flag" 2>nul
    )
)

REM Get current directory
set "CURRENT_DIR=%~dp0"

REM Launch main application with smart trial/activation system
if exist "%CURRENT_DIR%mangoo-juice-shop-management.exe" (
    REM Application features:
    REM - 30-day trial period per device
    REM - When copied to new device: automatic new trial
    REM - After trial expires: mandatory activation window
    REM - Activation codes: MANGOO2024, FARES123, JUICE789, ADMIN999, SYSTEM456

    REM Start with optimized settings - minimized window
    start /min "" "%CURRENT_DIR%mangoo-juice-shop-management.exe" --disable-gpu --disable-gpu-sandbox --disable-software-rasterizer --no-sandbox --disable-web-security --disable-features=VizDisplayCompositor --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-field-trial-config

    REM Close this launcher window immediately after starting
    exit /b
) else (
    echo ERROR: Main executable not found!
    echo Please ensure mangoo-juice-shop-management.exe exists in this folder.
    echo.
    echo For support contact:
    echo Phone: 0569329925
    echo Email: <EMAIL>
    pause
    exit /b 1
)

REM Function to create desktop shortcut (built-in, no external dependencies)
:CreateDesktopShortcut
setlocal
REM Try different desktop paths
set "DESKTOP=%USERPROFILE%\Desktop"
if not exist "%DESKTOP%" set "DESKTOP=%USERPROFILE%\OneDrive\Desktop"
if not exist "%DESKTOP%" set "DESKTOP=%PUBLIC%\Desktop"
set "SHORTCUT_NAME=Mangoo_System.lnk"
set "TARGET_PATH=%~dp0START_MANGOO_WORKING.bat"
set "ICON_PATH=%~dp0resources\app\assets\mangoo-icon.ico"

REM Create VBScript temporarily to create shortcut
set "VBS_FILE=%TEMP%\create_shortcut_%RANDOM%.vbs"

REM Write VBScript content with proper escaping
(
echo Set objShell = CreateObject^("WScript.Shell"^)
echo Set objShortcut = objShell.CreateShortcut^("%DESKTOP%\%SHORTCUT_NAME%"^)
echo objShortcut.TargetPath = "%TARGET_PATH%"
echo objShortcut.WorkingDirectory = "%~dp0"
echo objShortcut.Description = "Mangoo System - Smart Juice Shop Management"
echo objShortcut.WindowStyle = 7
if exist "%ICON_PATH%" echo objShortcut.IconLocation = "%ICON_PATH%"
echo objShortcut.Save
) > "%VBS_FILE%"

REM Execute VBScript and clean up
cscript //nologo "%VBS_FILE%" >nul 2>&1
del "%VBS_FILE%" >nul 2>&1
endlocal
goto :eof
