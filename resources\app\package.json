{"name": "mangoo-juice-shop-management", "version": "2.3.0", "description": "Mangoo - Smart Juice Shop Management System with Enhanced Backup & Import System v2.3.0", "main": "main.js", "scripts": {"start": "electron .", "start-hidden": "electron . --no-console", "start-production": "set NODE_ENV=production && electron .", "build": "electron-builder", "build-win": "electron-builder --win --x64", "build-portable": "electron-builder --win --x64 --config.win.target=portable", "build-installer": "electron-builder --win --x64 --config.win.target=nsis", "build-all": "electron-builder --win --x64 --config.win.target=portable --config.win.target=nsis", "dist": "electron-builder --publish=never", "pack": "electron-packager . --platform=win32 --arch=x64 --out=dist/ --overwrite", "clean": "<PERSON><PERSON><PERSON> dist", "rebuild": "npm run clean && npm run build-win", "test-build": "npm run build-portable"}, "keywords": ["mangoo", "juice-shop", "management", "arabic", "pos", "inventory", "smart-system", "backup-system", "import-export", "data-management", "enhanced-v2.3.0"], "author": "Fares Nawaf <<EMAIL>>", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "electron-packager": "^17.1.2", "rimraf": "^6.0.1"}, "build": {"appId": "com.faresnawaf.mangoo", "productName": "🥭 Mangoo - Smart Juice Shop Management System", "directories": {"output": "dist", "buildResources": "assets"}, "files": ["main.js", "renderer.js", "index.html", "assets/**/*", "data/**/*", "!node_modules/**/*", "!dist/**/*", "!*.vbs", "!*.bat"], "extraFiles": [{"from": "data", "to": "data", "filter": ["**/*"]}], "win": {"target": "portable", "icon": "assets/mangoo-icon.ico", "sign": false}, "portable": {"artifactName": "🥭_Mangoo_System_v${version}_Portable.exe", "requestExecutionLevel": "user"}, "nsis": {"differentialPackage": false}, "compression": "maximum", "removePackageScripts": true, "nodeGypRebuild": false, "buildDependenciesFromSource": false, "electronDownload": {"cache": "./electron-cache"}, "forceCodeSigning": false, "publish": null, "afterSign": null, "afterAllArtifactBuild": null}}