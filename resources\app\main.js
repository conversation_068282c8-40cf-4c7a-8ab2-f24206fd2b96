const { app, BrowserWindow, Menu, dialog, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');
const os = require('os');
const crypto = require('crypto');

// ===== دعم نظام النسخ الاحتياطي والاستيراد المحسن v2.3.0 =====
// تطوير: Fares Nawaf - 0569329925
// تاريخ التطوير: يونيو 2025

// متغيرات النظام الجديد
let backupInProgress = false;
let restoreInProgress = false;

// تعطيل تحذيرات الأمان في بيئة التطوير
process.env['ELECTRON_DISABLE_SECURITY_WARNINGS'] = 'true';

let mainWindow;
let isQuitting = false; // متغير للتحكم في الإغلاق

// دالة الحصول على أيقونة التطبيق
function getAppIcon() {
    const iconPaths = [
        path.join(__dirname, 'assets', 'mangoo-icon.ico'),
        path.join(__dirname, 'assets', 'icon.png'),
        path.join(__dirname, 'assets', 'mangoo-icon.png')
    ];

    for (const iconPath of iconPaths) {
        if (fs.existsSync(iconPath)) {
            console.log(`✅ استخدام أيقونة: ${iconPath}`);
            return iconPath;
        }
    }

    console.log('⚠️ لم يتم العثور على أيقونة مخصصة، استخدام الافتراضية');
    return null;
}

// دالة التحقق من عمل الاختصار محسنة
function testDesktopShortcut() {
    try {
        const desktopPath = path.join(os.homedir(), 'Desktop');
        const possibleShortcuts = [
            path.join(desktopPath, '🥭 Mangoo System.lnk'),
            path.join(desktopPath, 'Mangoo System.lnk'),
            path.join(desktopPath, '.mangoo.lnk'),
            path.join(desktopPath, '🥭 Mangoo.lnk')
        ];

        for (const shortcutPath of possibleShortcuts) {
            if (fs.existsSync(shortcutPath)) {
                console.log('✅ اختصار سطح المكتب موجود:', shortcutPath);
                return true;
            }
        }

        console.log('❌ لم يتم العثور على اختصار سطح المكتب');
        return false;
    } catch (error) {
        console.error('خطأ في التحقق من اختصار سطح المكتب:', error);
        return false;
    }
}

// دالة إنشاء اختصار سطح المكتب محسنة
function createDesktopShortcut() {
    try {
        const { spawn } = require('child_process');
        const currentPath = process.cwd();
        const desktopPath = path.join(os.homedir(), 'Desktop');
        const shortcutPath = path.join(desktopPath, '🥭 Mangoo System.lnk');

        console.log('🔄 بدء إنشاء اختصار سطح المكتب...');
        console.log('📁 المسار الحالي:', currentPath);
        console.log('🖥️ مسار سطح المكتب:', desktopPath);

        // التحقق من وجود الاختصار مسبقاً
        if (fs.existsSync(shortcutPath)) {
            console.log('🖥️ اختصار سطح المكتب موجود مسبقاً');
            showSuccessNotification();
            return;
        }

        // البحث عن ملف التشغيل الصحيح للنسخة المحمولة
        const possibleTargets = [
            path.join(currentPath, 'mangoo-juice-shop-management.exe'),
            path.join(currentPath, 'START_MANGOO_WORKING.bat'),
            path.join(currentPath, '🥭_MANGOO_PORTABLE.bat'),
            path.join(currentPath, 'Start_Mangoo_Hidden.vbs'),
            path.join(currentPath, '🥭_Mangoo_System.exe'),
            path.join(currentPath, 'تشغيل_نظام_Mangoo.bat'),
            path.join(currentPath, 'start_mangoo.vbs'),
            path.join(currentPath, 'npm_start.vbs')
        ];

        let targetFile = null;
        for (const target of possibleTargets) {
            if (fs.existsSync(target)) {
                targetFile = target;
                console.log('✅ تم العثور على ملف التشغيل:', target);
                break;
            }
        }

        // إذا لم نجد أي ملف، أنشئ ملف تشغيل للنسخة المحمولة
        if (!targetFile) {
            // التحقق من وجود الملف التنفيذي الرئيسي
            const mainExe = path.join(currentPath, 'mangoo-juice-shop-management.exe');
            if (fs.existsSync(mainExe)) {
                targetFile = path.join(currentPath, 'Start_Mangoo_Desktop.bat');
                const startScript = `@echo off
title Mangoo System - Desktop Shortcut Launcher
cd /d "${currentPath}"
start "" "${mainExe}" --disable-gpu --disable-gpu-sandbox --disable-software-rasterizer --no-sandbox --disable-web-security
exit`;
            } else {
                targetFile = path.join(currentPath, 'start_mangoo.bat');
                const startScript = `@echo off
cd /d "${currentPath}"
start "" "${path.join(currentPath, '🥭_Mangoo_System.exe')}"`;
            }

            try {
                fs.writeFileSync(targetFile, startScript, 'utf8');
                console.log('✅ تم إنشاء ملف التشغيل:', targetFile);
            } catch (error) {
                console.log('❌ فشل في إنشاء ملف التشغيل:', error.message);
                showFailureNotification();
                return;
            }
        }

        // التحقق من وجود الأيقونة في مواقع مختلفة
        const possibleIconPaths = [
            path.join(currentPath, 'assets', 'mangoo-icon.ico'),
            path.join(currentPath, 'resources', 'app', 'assets', 'mangoo-icon.ico'),
            path.join(__dirname, 'assets', 'mangoo-icon.ico'),
            path.join(currentPath, 'mangoo-juice-shop-management.exe') // استخدام أيقونة الملف التنفيذي
        ];

        let iconPath = null;
        for (const possiblePath of possibleIconPaths) {
            if (fs.existsSync(possiblePath)) {
                iconPath = possiblePath;
                console.log('✅ تم العثور على الأيقونة:', iconPath);
                break;
            }
        }

        if (!iconPath) {
            console.log('⚠️ لم يتم العثور على أيقونة مخصصة، سيتم استخدام الأيقونة الافتراضية');
        }

        // إنشاء ملف VBScript محسن
        const vbsScript = `
Set objShell = CreateObject("WScript.Shell")
Set objShortcut = objShell.CreateShortcut("${shortcutPath.replace(/\\/g, '\\\\')}")
objShortcut.TargetPath = "${targetFile.replace(/\\/g, '\\\\')}"
objShortcut.WorkingDirectory = "${currentPath.replace(/\\/g, '\\\\')}"
objShortcut.Description = "🥭 Mangoo - نظام إدارة محل العصائر والحلويات الذكي"
${fs.existsSync(iconPath) ? `objShortcut.IconLocation = "${iconPath.replace(/\\/g, '\\\\')}"` : ''}
objShortcut.WindowStyle = 1
objShortcut.Save
        `.trim();

        const tempVbsPath = path.join(currentPath, 'temp_shortcut.vbs');
        fs.writeFileSync(tempVbsPath, vbsScript, 'utf8');

        console.log('🔄 تشغيل VBScript لإنشاء الاختصار...');

        // تشغيل VBScript
        const vbsProcess = spawn('cscript', ['//nologo', tempVbsPath], {
            windowsHide: true,
            stdio: ['ignore', 'pipe', 'pipe']
        });

        let output = '';
        let errorOutput = '';

        vbsProcess.stdout.on('data', (data) => {
            output += data.toString();
        });

        vbsProcess.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });

        vbsProcess.on('close', (code) => {
            // حذف الملف المؤقت
            try {
                fs.unlinkSync(tempVbsPath);
            } catch (error) {
                console.log('تعذر حذف الملف المؤقت');
            }

            console.log('📊 نتيجة VBScript - كود الخروج:', code);
            if (output) console.log('📤 المخرجات:', output);
            if (errorOutput) console.log('❌ الأخطاء:', errorOutput);

            if (code === 0 && fs.existsSync(shortcutPath)) {
                console.log('✅ تم إنشاء اختصار سطح المكتب بنجاح');
                showSuccessNotification();
            } else {
                console.log('❌ فشل في إنشاء الاختصار');
                showFailureNotification();
            }
        });

        vbsProcess.on('error', (error) => {
            console.error('❌ خطأ في تشغيل VBScript:', error);
            showFailureNotification();
        });

    } catch (error) {
        console.error('❌ خطأ في إنشاء الاختصار:', error);
        showFailureNotification();
    }
}

// دالة إظهار إشعار النجاح
function showSuccessNotification() {
    if (mainWindow && !mainWindow.isDestroyed()) {
        setTimeout(() => {
            mainWindow.webContents.executeJavaScript(`
                if (typeof showAlert === 'function') {
                    showAlert('🥭 تم إنشاء اختصار Mangoo في سطح المكتب بنجاح!\\n\\n✅ يمكنك الآن تشغيل نظام Mangoo من سطح المكتب مباشرة\\n🚀 استمتع بتجربة إدارة محلك بطريقة ذكية ومتطورة', 'success', 8000);
                }
            `);
        }, 3000);
    }
}

// دالة إظهار إشعار الفشل
function showFailureNotification() {
    if (mainWindow && !mainWindow.isDestroyed()) {
        setTimeout(() => {
            mainWindow.webContents.executeJavaScript(`
                if (typeof showAlert === 'function') {
                    showAlert('⚠️ لم يتم إنشاء الاختصار تلقائياً\\n\\n💡 يمكنك إنشاؤه يدوياً من قائمة مساعدة', 'warning', 6000);
                }
            `);
        }, 3000);
    }
}

// دالة إظهار إشعار النجاح
function showSuccessNotification() {
    if (mainWindow && !mainWindow.isDestroyed()) {
        setTimeout(() => {
            mainWindow.webContents.executeJavaScript(`
                if (typeof showAlert === 'function') {
                    showAlert('🥭 تم إنشاء اختصار .mangoo في سطح المكتب بنجاح!\\n\\n✅ يمكنك الآن تشغيل البرنامج من سطح المكتب مباشرة\\n🖱️ انقر نقراً مزدوجاً على الاختصار لتشغيل البرنامج\\n📍 مسار الاختصار: سطح المكتب\\\\.mangoo', 'success', 8000);
                }
            `);
        }, 3000);

        setTimeout(() => {
            mainWindow.webContents.executeJavaScript(`
                if (typeof showAlert === 'function') {
                    showAlert('🎉 مرحباً بك في نظام Mangoo!\\n\\n💡 نصيحة: يمكنك الآن إغلاق هذه النافذة واستخدام اختصار سطح المكتب للتشغيل السريع', 'info', 6000);
                }
            `);
        }, 13000);
    }
}









// نظام التفعيل والرقم السري
const ACTIVATION_CODES = [
    'MANGOO2024',
    'FARES123',
    'JUICE789',
    'ADMIN999',
    'SYSTEM456'
];

// مدة التجربة المجانية (30 يوم)
const TRIAL_PERIOD_DAYS = 30;

// دالة إنشاء معرف فريد للجهاز
function generateDeviceId() {
    try {
        // جمع معلومات فريدة عن الجهاز
        const hostname = os.hostname();
        const platform = os.platform();
        const arch = os.arch();
        const cpus = os.cpus();
        const totalMemory = os.totalmem();
        const userInfo = os.userInfo();

        // إنشاء نص فريد يجمع معلومات الجهاز
        const deviceInfo = [
            hostname,
            platform,
            arch,
            cpus.length,
            cpus[0] ? cpus[0].model : 'unknown',
            totalMemory,
            userInfo.username,
            userInfo.homedir
        ].join('|');

        // إنشاء hash فريد للجهاز
        const deviceId = crypto.createHash('sha256').update(deviceInfo).digest('hex');

        console.log('🔑 معرف الجهاز المُولد:', deviceId.substring(0, 16) + '...');
        return deviceId;
    } catch (error) {
        console.error('❌ خطأ في إنشاء معرف الجهاز:', error);
        // في حالة الخطأ، استخدم معرف بديل
        return crypto.createHash('sha256').update(os.hostname() + os.platform()).digest('hex');
    }
}

// دالة التحقق من صحة الجهاز
function validateDevice(savedDeviceId) {
    const currentDeviceId = generateDeviceId();
    const isValid = savedDeviceId === currentDeviceId;

    console.log('🔍 التحقق من صحة الجهاز:');
    console.log('📱 معرف الجهاز المحفوظ:', savedDeviceId ? savedDeviceId.substring(0, 16) + '...' : 'غير موجود');
    console.log('📱 معرف الجهاز الحالي:', currentDeviceId.substring(0, 16) + '...');
    console.log('✅ النتيجة:', isValid ? 'جهاز صحيح' : 'جهاز مختلف');

    return isValid;
}

// دالة تسجيل محاولات التفعيل المشبوهة
function logSuspiciousActivity(activity, details) {
    try {
        const logPath = path.join(__dirname, 'data', 'security.log');
        const logEntry = {
            timestamp: new Date().toISOString(),
            activity: activity,
            details: details,
            deviceInfo: {
                hostname: os.hostname(),
                platform: os.platform(),
                arch: os.arch(),
                username: os.userInfo().username,
                deviceId: generateDeviceId().substring(0, 16) + '...'
            }
        };

        const logLine = JSON.stringify(logEntry) + '\n';
        fs.appendFileSync(logPath, logLine, 'utf8');
        console.log('📝 تم تسجيل نشاط مشبوه:', activity);
    } catch (error) {
        console.error('❌ خطأ في تسجيل النشاط المشبوه:', error);
    }
}

// دالة التحقق من صحة الرقم السري
function validateActivationCode(code) {
    const isValid = ACTIVATION_CODES.includes(code.toUpperCase());

    if (isValid) {
        console.log('✅ رقم التفعيل صحيح:', code.toUpperCase());
        console.log('🔑 معرف الجهاز الحالي:', generateDeviceId().substring(0, 16) + '...');
    } else {
        console.log('❌ رقم التفعيل غير صحيح:', code);
    }

    return isValid;
}

// دالة التحقق من انتهاء الفترة التجريبية
function isTrialExpired() {
    const configPath = path.join(__dirname, 'data', 'app-config.json');

    try {
        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

            // إذا كان البرنامج مفعل، تحقق من صحة الجهاز
            if (config.isActivated === true) {
                // التحقق من معرف الجهاز إذا كان موجود
                if (config.deviceId) {
                    const isValidDevice = validateDevice(config.deviceId);
                    if (!isValidDevice) {
                        console.log('🔄 التفعيل من جهاز مختلف - إعادة تعيين للفترة التجريبية');
                        // إعادة تعيين للفترة التجريبية بدلاً من الإغلاق
                        config.isActivated = false;
                        config.activationCode = null;
                        config.activationDate = null;
                        config.deviceId = null;
                        config.firstRunDate = new Date().toISOString(); // بدء فترة تجريبية جديدة
                        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
                        return false; // السماح بالعمل في الفترة التجريبية الجديدة
                    }
                }
                console.log('✅ البرنامج مفعل والجهاز صحيح - لا توجد قيود زمنية');
                return false;
            }

            // التحقق من تاريخ التثبيت الأول
            if (config.firstRunDate) {
                const firstRunDate = new Date(config.firstRunDate);
                const currentDate = new Date();
                const daysDifference = Math.floor((currentDate - firstRunDate) / (1000 * 60 * 60 * 24));

                console.log(`📅 أيام الاستخدام: ${daysDifference} من ${TRIAL_PERIOD_DAYS}`);
                console.log(`📅 تاريخ التثبيت الأول: ${firstRunDate.toLocaleDateString()}`);
                console.log(`📅 التاريخ الحالي: ${currentDate.toLocaleDateString()}`);

                return daysDifference >= TRIAL_PERIOD_DAYS;
            }
        }

        console.log('⚠️ ملف الإعدادات غير موجود - اعتبار أنها المرة الأولى');
        return false; // إذا لم يوجد ملف الإعدادات، اعتبر أنها المرة الأولى
    } catch (error) {
        console.error('❌ خطأ في التحقق من انتهاء الفترة التجريبية:', error);
        return false;
    }
}

// دالة حساب الأيام المتبقية في الفترة التجريبية
function getRemainingTrialDays() {
    const configPath = path.join(__dirname, 'data', 'app-config.json');

    try {
        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

            if (config.firstRunDate) {
                const firstRunDate = new Date(config.firstRunDate);
                const currentDate = new Date();
                const daysDifference = Math.floor((currentDate - firstRunDate) / (1000 * 60 * 60 * 24));
                const remainingDays = TRIAL_PERIOD_DAYS - daysDifference;

                return Math.max(0, remainingDays);
            }
        }

        return TRIAL_PERIOD_DAYS; // إذا لم يوجد ملف، اعتبر أن الفترة كاملة متبقية
    } catch (error) {
        console.error('خطأ في حساب الأيام المتبقية:', error);
        return 0;
    }
}

// دالة التحقق من تفعيل البرنامج
function checkActivation() {
    const configPath = path.join(__dirname, 'data', 'app-config.json');

    try {
        if (fs.existsSync(configPath)) {
            const configData = fs.readFileSync(configPath, 'utf8');
            const config = JSON.parse(configData);

            console.log('📄 قراءة ملف التكوين:', {
                isActivated: config.isActivated,
                activationCode: config.activationCode,
                activationDate: config.activationDate,
                hasDeviceId: !!config.deviceId
            });

            // التحقق من التفعيل الأساسي
            if (config.isActivated !== true) {
                console.log('❌ البرنامج غير مفعل');
                return false;
            }

            // التحقق من معرف الجهاز
            if (config.deviceId) {
                const isValidDevice = validateDevice(config.deviceId);
                if (!isValidDevice) {
                    console.log('❌ التفعيل غير صالح - جهاز مختلف');

                    // تسجيل محاولة استخدام تفعيل من جهاز آخر
                    logSuspiciousActivity('UNAUTHORIZED_DEVICE_ACCESS', {
                        savedDeviceId: config.deviceId ? config.deviceId.substring(0, 16) + '...' : 'unknown',
                        currentDeviceId: generateDeviceId().substring(0, 16) + '...',
                        activationCode: config.activationCode,
                        activationDate: config.activationDate
                    });

                    // إلغاء التفعيل إذا كان الجهاز مختلف
                    config.isActivated = false;
                    config.activationCode = null;
                    config.activationDate = null;
                    config.deviceId = null;
                    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
                    return false;
                }
            } else {
                console.log('⚠️ لا يوجد معرف جهاز محفوظ - تفعيل قديم');
                // إذا كان التفعيل قديم بدون معرف جهاز، أضف معرف الجهاز الحالي
                config.deviceId = generateDeviceId();
                fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
                console.log('✅ تم إضافة معرف الجهاز للتفعيل الموجود');
            }

            console.log('✅ التفعيل صالح والجهاز صحيح');
            return true;
        } else {
            console.log('⚠️ ملف التكوين غير موجود:', configPath);
        }
        return false;
    } catch (error) {
        console.error('❌ خطأ في التحقق من التفعيل:', error);
        return false;
    }
}

// دالة حفظ حالة التفعيل
function saveActivation(activationCode) {
    const configPath = path.join(__dirname, 'data', 'app-config.json');

    try {
        console.log('💾 بدء حفظ التفعيل...');

        // إنشاء مجلد البيانات إذا لم يكن موجوداً
        const dataDir = path.dirname(configPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
            console.log('📁 تم إنشاء مجلد البيانات');
        }

        let config = {};
        if (fs.existsSync(configPath)) {
            const existingData = fs.readFileSync(configPath, 'utf8');
            config = JSON.parse(existingData);
            console.log('📄 تم قراءة ملف التكوين الموجود');
        } else {
            console.log('📄 إنشاء ملف تكوين جديد');
        }

        // حفظ بيانات التفعيل
        config.isActivated = true;
        config.activationCode = activationCode.toLowerCase();
        config.activationDate = new Date().toISOString();
        config.deviceId = generateDeviceId(); // ربط التفعيل بالجهاز
        config.firstRunCompleted = true;
        config.firstRunDate = config.firstRunDate || new Date().toISOString();
        config.version = '1.0.0';

        const configJson = JSON.stringify(config, null, 2);
        fs.writeFileSync(configPath, configJson, 'utf8');

        console.log('✅ تم حفظ التفعيل بنجاح في:', configPath);
        console.log('📄 محتوى الملف المحفوظ:', config);

        // تسجيل التفعيل الناجح
        logSuspiciousActivity('SUCCESSFUL_ACTIVATION', {
            activationCode: activationCode,
            deviceId: config.deviceId.substring(0, 16) + '...',
            activationDate: config.activationDate
        });

        // التحقق من الحفظ
        if (fs.existsSync(configPath)) {
            const savedData = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            if (savedData.isActivated === true) {
                console.log('✅ تم التأكد من حفظ التفعيل بنجاح');
                return true;
            } else {
                console.error('❌ فشل في التأكد من حفظ التفعيل');
                return false;
            }
        } else {
            console.error('❌ الملف غير موجود بعد الحفظ');
            return false;
        }

    } catch (error) {
        console.error('❌ خطأ في حفظ التفعيل:', error);
        return false;
    }
}

// دالة التحقق من التشغيل الأول
function checkFirstRun() {
    const configPath = path.join(__dirname, 'data', 'app-config.json');

    try {
        // التحقق من وجود ملف الإعدادات
        if (fs.existsSync(configPath)) {
            const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
            if (config.firstRunCompleted) {
                console.log('🔄 ليس التشغيل الأول');
                return false;
            }
        }

        console.log('🆕 التشغيل الأول - سيتم إنشاء اختصار سطح المكتب');

        // إنشاء مجلد البيانات إذا لم يكن موجوداً
        const dataDir = path.dirname(configPath);
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // حفظ إعداد التشغيل الأول
        const config = {
            firstRunCompleted: true,
            firstRunDate: new Date().toISOString(),
            version: '1.0.0',
            isActivated: false,
            activationCode: null,
            activationDate: null,
            deviceId: null, // سيتم تعيينه عند التفعيل
            deviceInfo: {
                hostname: os.hostname(),
                platform: os.platform(),
                arch: os.arch(),
                username: os.userInfo().username
            }
        };

        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));

        return true;

    } catch (error) {
        console.error('خطأ في التحقق من التشغيل الأول:', error);
        return false;
    }
}

// دالة عرض رسالة تحذيرية للجهاز غير المصرح به
function showUnauthorizedDeviceDialog() {
    return new Promise((resolve) => {
        const warningWindow = new BrowserWindow({
            width: 600,
            height: 500,
            modal: true,
            parent: mainWindow,
            resizable: false,
            minimizable: false,
            maximizable: false,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false
            },
            icon: getAppIcon(),
            title: '⚠️ جهاز غير مصرح به',
            show: false
        });

        const warningHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>جهاز غير مصرح به</title>
            <style>
                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
                    color: white;
                    text-align: center;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                .warning-container {
                    background: rgba(255,255,255,0.1);
                    padding: 2rem;
                    border-radius: 15px;
                    backdrop-filter: blur(10px);
                    border: 1px solid rgba(255,255,255,0.2);
                    max-width: 500px;
                }
                .warning-icon {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                }
                .warning-title {
                    font-size: 1.8rem;
                    font-weight: bold;
                    margin-bottom: 1rem;
                }
                .warning-message {
                    font-size: 1.1rem;
                    line-height: 1.6;
                    margin-bottom: 2rem;
                }
                .btn {
                    background: white;
                    color: #dc3545;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    font-size: 1rem;
                    font-weight: bold;
                    cursor: pointer;
                    margin: 0 10px;
                }
                .btn:hover {
                    background: #f8f9fa;
                }
            </style>
        </head>
        <body>
            <div class="warning-container">
                <div class="warning-icon">🚫</div>
                <div class="warning-title">جهاز غير مصرح به</div>
                <div class="warning-message">
                    تم اكتشاف أن هذا البرنامج تم نسخه من جهاز آخر.<br><br>

                    <strong>⚠️ التفعيل مرتبط بالجهاز الأصلي فقط</strong><br><br>

                    للحصول على تفعيل لهذا الجهاز:<br>
                    📱 اتصل بـ: 0569329925<br>
                    📧 إيميل: <EMAIL><br><br>

                    <small>سيتم إغلاق البرنامج الآن</small>
                </div>
                <button class="btn" onclick="closeApp()">إغلاق البرنامج</button>
            </div>

            <script>
                const { ipcRenderer } = require('electron');

                function closeApp() {
                    ipcRenderer.invoke('unauthorized-device-close');
                }

                // إغلاق تلقائي بعد 30 ثانية
                setTimeout(() => {
                    closeApp();
                }, 30000);
            </script>
        </body>
        </html>
        `;

        warningWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(warningHTML));

        warningWindow.on('closed', () => {
            resolve();
        });

        warningWindow.once('ready-to-show', () => {
            warningWindow.show();
        });

        // معالج إغلاق التطبيق
        ipcMain.handle('unauthorized-device-close', async () => {
            warningWindow.close();
            app.quit();
            resolve();
        });
    });
}

// دالة عرض نافذة التفعيل الإجبارية (كبيرة)
function showMandatoryActivationDialog() {
    return new Promise((resolve) => {
        const activationWindow = new BrowserWindow({
            width: 900,
            height: 800,
            modal: true,
            parent: mainWindow,
            resizable: false,
            minimizable: false,
            maximizable: false,
            closable: false, // منع الإغلاق
            alwaysOnTop: true, // دائماً في المقدمة
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false
            },
            icon: getAppIcon(),
            title: 'تفعيل نظام Mangoo - مطلوب',
            show: false
        });

        const activationHTML = `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تفعيل نظام Mangoo - مطلوب</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: #333;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    padding: 20px;
                    margin: 0;
                    overflow-y: auto;
                }

                .activation-container {
                    background: white;
                    border-radius: 20px;
                    padding: 3rem;
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                    text-align: center;
                    max-width: 700px;
                    width: 90%;
                    position: relative;
                    animation: slideIn 0.5s ease-out;
                }

                @keyframes slideIn {
                    from {
                        opacity: 0;
                        transform: translateY(-50px);
                    }
                    to {
                        opacity: 1;
                        transform: translateY(0);
                    }
                }

                .logo {
                    font-size: 5rem;
                    margin-bottom: 1rem;
                    animation: pulse 2s infinite;
                }

                @keyframes pulse {
                    0%, 100% { transform: scale(1); }
                    50% { transform: scale(1.1); }
                }

                .title {
                    font-size: 2.5rem;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 1rem;
                }

                .subtitle {
                    font-size: 1.3rem;
                    color: #7f8c8d;
                    margin-bottom: 2rem;
                }

                .trial-expired {
                    background: #e74c3c;
                    color: white;
                    padding: 1.5rem;
                    border-radius: 15px;
                    margin-bottom: 2rem;
                    font-size: 1.2rem;
                    font-weight: bold;
                }

                .input-group {
                    margin: 2rem 0;
                }

                .input-label {
                    display: block;
                    font-size: 1.2rem;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 1rem;
                }

                .activation-input {
                    width: 100%;
                    padding: 1.5rem;
                    font-size: 1.5rem;
                    border: 3px solid #3498db;
                    border-radius: 15px;
                    text-align: center;
                    font-weight: bold;
                    letter-spacing: 2px;
                    text-transform: uppercase;
                    transition: all 0.3s ease;
                }

                .activation-input:focus {
                    outline: none;
                    border-color: #2980b9;
                    box-shadow: 0 0 20px rgba(52, 152, 219, 0.3);
                    transform: scale(1.02);
                }

                .btn {
                    background: linear-gradient(45deg, #3498db, #2980b9);
                    color: white;
                    border: none;
                    padding: 1.5rem 3rem;
                    font-size: 1.3rem;
                    font-weight: bold;
                    border-radius: 15px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    margin: 1rem;
                    min-width: 200px;
                }

                .btn:hover {
                    transform: translateY(-3px);
                    box-shadow: 0 10px 20px rgba(0,0,0,0.2);
                }

                .btn:active {
                    transform: translateY(0);
                }

                .contact-info {
                    background: #f8f9fa;
                    padding: 2rem;
                    border-radius: 15px;
                    margin-top: 2rem;
                    border-right: 5px solid #3498db;
                }

                .contact-title {
                    font-size: 1.3rem;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 1rem;
                }

                .contact-item {
                    font-size: 1.1rem;
                    margin: 0.5rem 0;
                    color: #34495e;
                }

                .error-message {
                    background: #e74c3c;
                    color: white;
                    padding: 1rem;
                    border-radius: 10px;
                    margin: 1rem 0;
                    font-weight: bold;
                    display: none;
                }

                .warning-text {
                    background: #f39c12;
                    color: white;
                    padding: 1.5rem;
                    border-radius: 15px;
                    margin: 2rem 0;
                    font-size: 1.1rem;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <div class="activation-container">
                <div class="logo">🔐</div>
                <div class="title">انتهت الفترة التجريبية</div>
                <div class="subtitle">نظام Mangoo لإدارة محل العصائر والحلويات</div>

                <div class="trial-expired">
                    ⏰ انتهت فترة التجربة المجانية (30 يوم)
                    <br>
                    يجب إدخال رقم التفعيل للمتابعة
                </div>

                <div class="warning-text">
                    ⚠️ لا يمكن إغلاق هذه النافذة إلا بعد إدخال رقم تفعيل صحيح
                </div>

                <div class="input-group">
                    <label class="input-label" for="activationCode">🔑 رقم التفعيل:</label>
                    <input
                        type="text"
                        id="activationCode"
                        class="activation-input"
                        placeholder="أدخل رقم التفعيل هنا"
                        maxlength="20"
                        autocomplete="off"
                    >
                </div>

                <div class="error-message" id="errorMessage"></div>

                <div style="margin: 2rem 0;">
                    <button class="btn" onclick="activateProgram()">
                        🚀 تفعيل البرنامج
                    </button>
                    <button class="btn" onclick="closeApplication()" style="background: linear-gradient(45deg, #e74c3c, #c0392b); margin-right: 1rem;">
                        ❌ إغلاق البرنامج
                    </button>
                </div>

                <div class="contact-info">
                    <div class="contact-title">📞 للحصول على رقم التفعيل:</div>
                    <div class="contact-item">📱 الهاتف: 0569329925</div>
                    <div class="contact-item">📧 الإيميل: <EMAIL></div>
                    <div class="contact-item">💬 واتساب: 0569329925</div>
                    <div class="contact-item">👨‍💻 المطور: Fares Nawaf</div>
                </div>
            </div>

            <script>
                const { ipcRenderer } = require('electron');

                function showError(message) {
                    const errorDiv = document.getElementById('errorMessage');
                    errorDiv.textContent = message;
                    errorDiv.style.display = 'block';

                    setTimeout(() => {
                        errorDiv.style.display = 'none';
                    }, 5000);
                }

                function closeApplication() {
                    if (confirm('هل أنت متأكد من إغلاق البرنامج؟\\n\\nلن تتمكن من استخدام البرنامج بدون تفعيل.')) {
                        ipcRenderer.invoke('close-application-from-mandatory');
                    }
                }

                function activateProgram() {
                    const code = document.getElementById('activationCode').value.trim();

                    if (!code) {
                        showError('يرجى إدخال رقم التفعيل');
                        return;
                    }

                    // إرسال رقم التفعيل للتحقق
                    ipcRenderer.invoke('validate-mandatory-activation', code).then(result => {
                        if (result.success) {
                            // تفعيل ناجح
                            document.body.innerHTML = \`
                                <div class="activation-container">
                                    <div class="logo">✅</div>
                                    <div class="title">تم التفعيل بنجاح!</div>
                                    <div class="subtitle">مرحباً بك في نظام Mangoo</div>
                                    <p style="margin: 2rem 0; color: #28a745; font-size: 1.3rem; font-weight: bold;">
                                        🎉 تم تفعيل البرنامج بنجاح<br>
                                        سيتم إغلاق هذه النافذة وفتح البرنامج الرئيسي
                                    </p>
                                </div>
                            \`;

                            setTimeout(() => {
                                ipcRenderer.invoke('mandatory-activation-complete');
                            }, 3000);
                        } else {
                            showError(result.message || 'رقم التفعيل غير صحيح');
                            document.getElementById('activationCode').value = '';
                            document.getElementById('activationCode').focus();
                        }
                    });
                }

                // التركيز على حقل الإدخال
                document.getElementById('activationCode').focus();

                // التفعيل بالضغط على Enter
                document.getElementById('activationCode').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        activateProgram();
                    }
                });

                // منع إغلاق النافذة بـ Alt+F4 أو Escape
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'F4' && e.altKey) {
                        e.preventDefault();
                        showError('لا يمكن إغلاق النافذة إلا بعد التفعيل أو إغلاق البرنامج');
                    }
                    if (e.key === 'Escape') {
                        e.preventDefault();
                        showError('لا يمكن إغلاق النافذة إلا بعد التفعيل أو إغلاق البرنامج');
                    }
                });
            </script>
        </body>
        </html>
        `;

        activationWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(activationHTML));

        // منع إغلاق النافذة إلا عند إغلاق التطبيق
        activationWindow.on('close', (event) => {
            if (!isQuitting) {
                event.preventDefault();
                console.log('🚫 محاولة إغلاق نافذة التفعيل الإجبارية - تم المنع');
                // عرض رسالة للمستخدم
                activationWindow.webContents.executeJavaScript(`
                    showError('لا يمكن إغلاق النافذة. يرجى التفعيل أو إغلاق البرنامج بالكامل.');
                `);
            }
        });

        activationWindow.once('ready-to-show', () => {
            activationWindow.show();
            activationWindow.focus();
        });

        // معالجة أحداث التفعيل الإجباري
        ipcMain.handle('validate-mandatory-activation', async (event, code) => {
            if (validateActivationCode(code)) {
                const saved = saveActivation(code);
                if (saved) {
                    return { success: true, message: 'تم التفعيل بنجاح' };
                } else {
                    return { success: false, message: 'خطأ في حفظ التفعيل' };
                }
            } else {
                return { success: false, message: 'رقم التفعيل غير صحيح' };
            }
        });

        ipcMain.handle('mandatory-activation-complete', async () => {
            activationWindow.removeAllListeners('close'); // إزالة منع الإغلاق
            activationWindow.close();
            resolve(true);
        });

        ipcMain.handle('close-application-from-mandatory', async () => {
            console.log('🚪 المستخدم اختار إغلاق التطبيق من نافذة التفعيل الإجبارية');
            activationWindow.removeAllListeners('close'); // إزالة منع الإغلاق
            activationWindow.close();
            // إغلاق التطبيق بالكامل
            setTimeout(() => {
                app.quit();
            }, 500);
            resolve(false);
        });
    });
}

// دالة عرض نافذة التفعيل العادية (للاستخدام من القائمة)
function showActivationDialog() {
    return new Promise((resolve) => {
        const activationWindow = new BrowserWindow({
            width: 500,
            height: 600,
            modal: true,
            parent: mainWindow,
            resizable: false,
            minimizable: false,
            maximizable: false,
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false
            },
            icon: getAppIcon(),
            title: 'تفعيل نظام Mangoo',
            show: false
        });

        const activationHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تفعيل نظام Mangoo</title>
            <style>
                * {
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: #333;
                    direction: rtl;
                    text-align: right;
                    height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }

                .activation-container {
                    background: white;
                    border-radius: 15px;
                    padding: 2rem;
                    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
                    text-align: center;
                    max-width: 450px;
                    width: 90%;
                }

                .logo {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                }

                .title {
                    font-size: 1.8rem;
                    font-weight: bold;
                    color: #333;
                    margin-bottom: 0.5rem;
                }

                .subtitle {
                    font-size: 1rem;
                    color: #666;
                    margin-bottom: 2rem;
                }

                .info-box {
                    background: #f8f9fa;
                    border: 2px solid #e9ecef;
                    border-radius: 8px;
                    padding: 1rem;
                    margin-bottom: 1.5rem;
                    font-size: 0.9rem;
                    line-height: 1.6;
                    color: #495057;
                }

                .form-group {
                    margin-bottom: 1.5rem;
                    text-align: right;
                }

                label {
                    display: block;
                    margin-bottom: 0.5rem;
                    font-weight: bold;
                    color: #333;
                }

                input[type="text"] {
                    width: 100%;
                    padding: 0.75rem;
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    font-size: 1rem;
                    text-align: center;
                    font-family: monospace;
                    letter-spacing: 2px;
                    text-transform: uppercase;
                }

                input[type="text"]:focus {
                    outline: none;
                    border-color: #667eea;
                    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
                }

                .btn {
                    padding: 0.75rem 1.5rem;
                    border: none;
                    border-radius: 8px;
                    font-size: 1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s;
                    margin: 0.25rem;
                }

                .btn:hover {
                    transform: translateY(-2px);
                    box-shadow: 0 4px 12px rgba(0,0,0,0.2);
                }

                .btn:first-child {
                    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                    color: white;
                }

                .btn-secondary {
                    background: #6c757d;
                    color: white;
                }

                .error-message {
                    color: #dc3545;
                    font-size: 0.9rem;
                    margin-top: 0.5rem;
                    display: none;
                }

                .contact-info {
                    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
                    color: white;
                    padding: 1rem;
                    border-radius: 8px;
                    margin-bottom: 1rem;
                    font-size: 0.9rem;
                }
            </style>
        </head>
        <body>
            <div class="activation-container">
                <div class="logo">🥭</div>
                <div class="title">نظام Mangoo</div>
                <div class="subtitle">نظام إدارة محل العصائر والحلويات الذكي</div>

                <div class="info-box">
                    🔐 يرجى إدخال رقم التفعيل للمتابعة<br>
                    💡 للحصول على رقم التفعيل، تواصل مع المطور:<br>
                    📱 الهاتف: 0569329925<br>
                    📧 الإيميل: <EMAIL><br>
                    💬 واتساب: 0569329925
                </div>

                <div class="contact-info" style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); margin-bottom: 1rem;">
                    <strong>⚠️ تنبيه مهم:</strong><br>
                    🔒 التفعيل مرتبط بهذا الجهاز فقط<br>
                    🚫 لا يمكن نقل التفعيل إلى جهاز آخر<br>
                    💻 كل جهاز يحتاج تفعيل منفصل
                </div>

                <div class="contact-info">
                    <strong>📞 للحصول على رقم التفعيل:</strong><br>
                    🔹 اتصل أو أرسل واتساب: 0569329925<br>
                    🔹 أرسل إيميل: <EMAIL><br>
                    🔹 أوقات العمل: 9 صباحاً - 10 مساءً

                    <div style="background: #e8f5e8; border: 1px solid #28a745; border-radius: 6px; padding: 0.5rem; margin-top: 0.75rem; color: #155724;">
                        <strong>⚡ تفعيل فوري:</strong> سيتم إرسال رقم التفعيل خلال دقائق من التواصل
                    </div>
                </div>

                <div class="form-group">
                    <label for="activationCode">رقم التفعيل:</label>
                    <input type="text" id="activationCode" placeholder="أدخل رقم التفعيل" maxlength="20">
                    <div class="error-message" id="errorMessage">رقم التفعيل غير صحيح</div>
                </div>

                <div style="margin-bottom: 1rem;">
                    <button class="btn" onclick="activateProgram()">🚀 تفعيل البرنامج</button>
                    <button class="btn btn-secondary" onclick="exitProgram()">❌ خروج</button>
                </div>

                <div style="font-size: 0.8rem; color: #666; margin-top: 1rem;">
                    <strong>أرقام التفعيل للاختبار:</strong><br>
                    MANGOO2024 | FARES123 | JUICE789 | ADMIN999 | SYSTEM456
                </div>
            </div>

            <script>
                const { ipcRenderer } = require('electron');

                function showError(message) {
                    const errorDiv = document.getElementById('errorMessage');
                    errorDiv.textContent = message;
                    errorDiv.style.display = 'block';

                    setTimeout(() => {
                        errorDiv.style.display = 'none';
                    }, 3000);
                }

                function exitProgram() {
                    ipcRenderer.invoke('activation-cancelled');
                }

                function activateProgram() {
                    const code = document.getElementById('activationCode').value.trim();
                    const errorDiv = document.getElementById('errorMessage');

                    if (!code) {
                        showError('يرجى إدخال رقم التفعيل');
                        return;
                    }

                    // إرسال رقم التفعيل للتحقق
                    ipcRenderer.invoke('validate-activation', code).then(result => {
                        if (result.success) {
                            // تفعيل ناجح
                            document.body.innerHTML = \`
                                <div class="activation-container">
                                    <div class="logo">✅</div>
                                    <div class="title">تم التفعيل بنجاح!</div>
                                    <div class="subtitle">مرحباً بك في نظام Mangoo</div>
                                    <p style="margin: 2rem 0; color: #28a745; font-size: 1.1rem;">
                                        🎉 تم تفعيل البرنامج بنجاح<br>
                                        سيتم إغلاق هذه النافذة وفتح البرنامج الرئيسي
                                    </p>
                                </div>
                            \`;

                            setTimeout(() => {
                                ipcRenderer.invoke('activation-complete');
                            }, 2000);
                        } else {
                            showError(result.message || 'رقم التفعيل غير صحيح');
                        }
                    });
                }

                // التركيز على حقل الإدخال
                document.getElementById('activationCode').focus();

                // التفعيل بالضغط على Enter
                document.getElementById('activationCode').addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        activateProgram();
                    }
                });
            </script>
        </body>
        </html>
        `;

        activationWindow.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(activationHTML));

        activationWindow.on('closed', () => {
            resolve(false);
        });

        activationWindow.once('ready-to-show', () => {
            activationWindow.show();
        });

        // معالجة أحداث التفعيل
        ipcMain.handle('validate-activation', async (event, code) => {
            if (validateActivationCode(code)) {
                const saved = saveActivation(code);
                if (saved) {
                    return { success: true, message: 'تم التفعيل بنجاح' };
                } else {
                    return { success: false, message: 'خطأ في حفظ التفعيل' };
                }
            } else {
                return { success: false, message: 'رقم التفعيل غير صحيح' };
            }
        });

        ipcMain.handle('activation-complete', async () => {
            activationWindow.close();
            resolve(true);
        });

        ipcMain.handle('activation-cancelled', async () => {
            activationWindow.close();
            resolve(false);
        });
    });
}

// دالة إغلاق قوية محسنة لـ Windows
function forceQuit() {
    console.log('🔥 بدء الإغلاق القوي للتطبيق (Windows)');
    isQuitting = true;

    // تشغيل مؤقت الإغلاق القوي
    setForceQuitTimer();

    try {
        // إغلاق النافذة الرئيسية
        if (mainWindow && !mainWindow.isDestroyed()) {
            console.log('🗂️ إغلاق النافذة الرئيسية');
            mainWindow.removeAllListeners('close');
            mainWindow.removeAllListeners('closed');
            mainWindow.removeAllListeners('session-end');
            mainWindow.destroy();
            mainWindow = null;
        }

        // إغلاق جميع النوافذ
        const allWindows = BrowserWindow.getAllWindows();
        allWindows.forEach(window => {
            if (!window.isDestroyed()) {
                window.removeAllListeners();
                window.destroy();
            }
        });

        console.log('✅ تم إغلاق جميع النوافذ');

        // إعدادات خاصة بـ Windows
        if (process.platform === 'win32') {
            try {
                const { exec } = require('child_process');

                // إغلاق عمليات Electron المتبقية
                console.log('🔄 إغلاق عمليات Electron في Windows');
                exec('taskkill /f /im electron.exe 2>nul', () => {});
                exec('taskkill /f /im "juice-shop-management.exe" 2>nul', () => {});

                // إغلاق نوافذ الكونسول
                exec('taskkill /f /im cmd.exe 2>nul', () => {});
                exec('taskkill /f /im conhost.exe 2>nul', () => {});

                // إغلاق عمليات Node.js المتبقية
                exec('taskkill /f /im node.exe 2>nul', () => {});

            } catch (error) {
                console.log('تعذر إغلاق العمليات:', error.message);
            }
        }

        // إنهاء التطبيق بطريقة متدرجة
        console.log('🚪 بدء إنهاء التطبيق');

        // المحاولة الأولى: app.quit()
        setTimeout(() => {
            console.log('🔄 المحاولة الأولى: app.quit()');
            if (forceQuitTimer) {
                clearTimeout(forceQuitTimer);
            }
            app.quit();
        }, 100);

        // المحاولة الثانية: process.exit(0)
        setTimeout(() => {
            console.log('🔄 المحاولة الثانية: process.exit(0)');
            process.exit(0);
        }, 1000);

        // المحاولة الثالثة: إنهاء قوي
        setTimeout(() => {
            console.log('⚡ المحاولة الثالثة: إنهاء قوي');
            process.kill(process.pid, 'SIGTERM');
        }, 3000);

    } catch (error) {
        console.error('❌ خطأ في الإغلاق القوي:', error);
        if (forceQuitTimer) {
            clearTimeout(forceQuitTimer);
        }
        // إنهاء فوري في حالة الخطأ
        process.exit(1);
    }
}

async function createWindow() {

    // إخفاء نافذة الكونسول في Windows
    if (process.platform === 'win32') {
        try {
            const { exec } = require('child_process');
            // إخفاء نافذة الكونسول الحالية
            exec('powershell -WindowStyle Hidden -Command "Add-Type -Name Window -Namespace Console -MemberDefinition \'[DllImport(\\\"Kernel32.dll\\\")] public static extern IntPtr GetConsoleWindow(); [DllImport(\\\"user32.dll\\\")] public static extern bool ShowWindow(IntPtr hWnd, Int32 nCmdShow);\'; [Console.Window]::ShowWindow([Console.Window]::GetConsoleWindow(), 0)"', () => {});
        } catch (error) {
            // في حالة فشل إخفاء الكونسول، تجاهل الخطأ
        }
    }

    // إنشاء النافذة الرئيسية مع إعدادات Windows
    mainWindow = new BrowserWindow({
        width: 1400,
        height: 900,
        minWidth: 1200,
        minHeight: 700,
        webPreferences: {
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false,
            // إعدادات إضافية لـ Windows
            backgroundThrottling: false,
            offscreen: false
        },
        icon: getAppIcon(),
        title: '🥭 Mangoo - نظام إدارة محل العصائر والحلويات الذكي',
        show: false,
        titleBarStyle: 'default',
        // إعدادات خاصة بـ Windows
        skipTaskbar: false,
        closable: true,
        minimizable: true,
        maximizable: true,
        resizable: true,
        // إعدادات الإغلاق
        autoHideMenuBar: false,
        useContentSize: false,
        // إعدادات الأمان
        sandbox: false,
        // إعدادات النافذة
        frame: true,
        transparent: false,
        hasShadow: true,
        // إخفاء نافذة الكونسول
        webPreferences: {
            ...mainWindow?.webPreferences,
            nodeIntegration: true,
            contextIsolation: false,
            enableRemoteModule: true,
            webSecurity: false,
            backgroundThrottling: false,
            offscreen: false
        }
    });

    // تحميل ملف HTML
    mainWindow.loadFile('index.html');

    // إظهار النافذة عند الانتهاء من التحميل
    mainWindow.once('ready-to-show', () => {
        mainWindow.show();

        // فتح أدوات المطور في بيئة التطوير
        if (process.env.NODE_ENV === 'development') {
            mainWindow.webContents.openDevTools();
        }

        // عرض معلومات الجهاز في وحدة التحكم
        console.log('💻 معلومات الجهاز:');
        console.log('🏷️ اسم الجهاز:', os.hostname());
        console.log('🖥️ نظام التشغيل:', os.platform(), os.arch());
        console.log('👤 المستخدم:', os.userInfo().username);
        console.log('🔑 معرف الجهاز:', generateDeviceId().substring(0, 16) + '...');

        // إضافة API التفعيل
        mainWindow.webContents.executeJavaScript(`
            window.activationAPI = {
                checkActivation: () => require('electron').ipcRenderer.invoke('check-activation'),
                showActivationDialog: () => require('electron').ipcRenderer.invoke('show-activation-dialog')
            };
            console.log('✅ تم تحميل API التفعيل بنجاح');

            // إجبار تحديث واجهة التفعيل فور تحميل API
            setTimeout(() => {
                console.log('🔧 إجبار تحديث واجهة التفعيل من main.js...');
                if (typeof window.forceUpdateActivationUI === 'function') {
                    window.forceUpdateActivationUI();
                } else {
                    // إذا لم تكن الدالة متاحة بعد، استخدم طريقة مباشرة
                    window.activationAPI.checkActivation().then(result => {
                        if (result.success && result.isActivated) {
                            console.log('✅ البرنامج مفعل - تحديث الواجهة مباشرة من main.js');

                            // تحديث زر التفعيل
                            const activateBtn = document.getElementById('activateBtn');
                            if (activateBtn) {
                                activateBtn.textContent = '✅ تم التفعيل بنجاح';
                                activateBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                                activateBtn.style.color = 'white';
                                activateBtn.classList.add('activated');
                                activateBtn.onclick = () => alert('البرنامج مفعل بالفعل! ✅');
                            }

                            // تحديث رسالة التفعيل
                            const trialMessage = document.getElementById('trialMessage');
                            if (trialMessage) {
                                trialMessage.textContent = '✅ تم تفعيل البرنامج بنجاح';
                                trialMessage.style.color = '#28a745';
                            }

                            // تحديث شريط التفعيل
                            const trialBar = document.getElementById('trialBar');
                            if (trialBar) {
                                trialBar.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                                trialBar.classList.add('show');
                                setTimeout(() => trialBar.classList.remove('show'), 8000);
                            }
                        }
                    }).catch(err => console.error('خطأ في التحقق من التفعيل:', err));
                }
            }, 2000);
        `);

        // التحقق من حالة التفعيل عند بدء التطبيق
        console.log('🔍 التحقق من حالة التفعيل عند بدء التطبيق...');

        // التحقق من وجود تفعيل من جهاز آخر - إعادة تعيين للفترة التجريبية
        const configPath = path.join(__dirname, 'data', 'app-config.json');
        if (fs.existsSync(configPath)) {
            try {
                const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                if (config.isActivated && config.deviceId && !validateDevice(config.deviceId)) {
                    console.log('🔄 تم اكتشاف نسخ البرنامج لجهاز جديد - إعادة تعيين للفترة التجريبية');
                    // إعادة تعيين التفعيل وبدء فترة تجريبية جديدة
                    config.isActivated = false;
                    config.activationCode = null;
                    config.activationDate = null;
                    config.deviceId = null;
                    config.firstRunDate = new Date().toISOString(); // بدء فترة تجريبية جديدة
                    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
                    console.log('✅ تم إعادة تعيين البرنامج للفترة التجريبية على الجهاز الجديد');
                }
            } catch (error) {
                console.error('خطأ في فحص ملف التكوين:', error);
            }
        }

        const activationStatus = checkActivation();
        const trialExpired = isTrialExpired();
        console.log('📋 حالة التفعيل الحالية:', activationStatus ? 'مفعل' : 'غير مفعل');
        console.log('📅 حالة الفترة التجريبية:', trialExpired ? 'منتهية' : 'سارية');

        // إذا انتهت الفترة التجريبية ولم يتم التفعيل، عرض نافذة التفعيل الإجبارية
        if (trialExpired && !activationStatus) {
            console.log('🔒 الفترة التجريبية منتهية - عرض نافذة التفعيل الإجبارية');
            setTimeout(() => {
                showMandatoryActivationDialog().then(activated => {
                    if (activated) {
                        console.log('✅ تم التفعيل بنجاح من النافذة الإجبارية');
                        // إعادة تحميل الواجهة لتحديث حالة التفعيل
                        if (mainWindow && !mainWindow.isDestroyed()) {
                            mainWindow.webContents.executeJavaScript(`
                                if (typeof window.forceUpdateActivationUI === 'function') {
                                    window.forceUpdateActivationUI();
                                }
                            `);
                        }
                    }
                });
            }, 2000);
        }

        // التحقق من التشغيل الأول وإنشاء اختصار سطح المكتب
        if (checkFirstRun()) {
            setTimeout(() => {
                createDesktopShortcut();
            }, 2000); // انتظار ثانيتين حتى يتم تحميل الواجهة بالكامل
        } else {
            // إذا لم يكن التشغيل الأول، تحقق من وجود الاختصار
            setTimeout(() => {
                const shortcutExists = testDesktopShortcut();
                if (!shortcutExists && mainWindow && !mainWindow.isDestroyed()) {
                    setTimeout(() => {
                        mainWindow.webContents.executeJavaScript(`
                            if (typeof showAlert === 'function') {
                                showAlert('💡 نصيحة: يمكنك إنشاء اختصار سطح المكتب من قائمة مساعدة > إنشاء اختصار سطح المكتب\\n\\nهذا سيسهل عليك تشغيل البرنامج مستقبلاً', 'info', 6000);
                            }
                        `);
                    }, 5000);
                }
            }, 3000);
        }
    });

    // إنشاء قائمة التطبيق
    createMenu();

    // التعامل مع إغلاق النافذة
    mainWindow.on('closed', () => {
        console.log('🗂️ النافذة الرئيسية مغلقة');
        mainWindow = null;
        // التأكد من إنهاء التطبيق
        if (isQuitting) {
            app.quit();
        }
    });

    // معالج إضافي للتأكد من الإغلاق من شريط المهام
    mainWindow.on('session-end', () => {
        console.log('💻 إنهاء جلسة النظام');
        isQuitting = true;
        forceQuit();
    });

    // التعامل مع إغلاق النافذة
    mainWindow.on('close', async (event) => {
        console.log('🔄 محاولة إغلاق النافذة، isQuitting:', isQuitting);

        // إذا كان الإغلاق مُخطط له، اسمح بالإغلاق
        if (isQuitting) {
            console.log('✅ إغلاق مُخطط له، السماح بالإغلاق');
            return;
        }

        // منع الإغلاق المباشر لإظهار حوار التأكيد
        event.preventDefault();
        console.log('⏸️ تم منع الإغلاق لإظهار حوار التأكيد');

        try {
            // التأكد من أن النافذة ما زالت موجودة
            if (!mainWindow || mainWindow.isDestroyed()) {
                console.log('❌ النافذة غير موجودة، إغلاق فوري');
                isQuitting = true;
                forceQuit();
                return;
            }

            // إظهار حوار تأكيد الحفظ
            console.log('💬 إظهار حوار تأكيد الحفظ');
            const choice = await dialog.showMessageBox(mainWindow, {
                type: 'question',
                buttons: ['حفظ والخروج', 'خروج بدون حفظ', 'إلغاء'],
                defaultId: 0,
                cancelId: 2,
                title: 'تأكيد إغلاق البرنامج',
                message: 'هل تريد حفظ البيانات قبل إغلاق البرنامج؟',
                detail: 'سيتم فقدان أي تغييرات غير محفوظة إذا لم تقم بالحفظ.',
                noLink: true
            });

            console.log('📝 اختيار المستخدم:', choice.response);

            if (choice.response === 0) {
                // حفظ والخروج
                console.log('💾 حفظ والخروج');
                try {
                    await mainWindow.webContents.executeJavaScript('window.saveAllData()');
                    console.log('✅ تم حفظ البيانات بنجاح');
                } catch (error) {
                    console.error('❌ خطأ في حفظ البيانات:', error);
                }
                // إغلاق فوري
                isQuitting = true;
                forceQuit();
            } else if (choice.response === 1) {
                // خروج بدون حفظ
                console.log('🚪 خروج بدون حفظ');
                isQuitting = true;
                forceQuit();
            } else {
                // إلغاء
                console.log('🚫 تم إلغاء الإغلاق');
                // لا نفعل شيء - النافذة تبقى مفتوحة
            }
        } catch (error) {
            console.error('❌ خطأ في حوار الخروج:', error);
            // في حالة الخطأ، اسمح بالإغلاق
            isQuitting = true;
            forceQuit();
        }
    });
}

function createMenu() {
    const template = [
        {
            label: 'ملف',
            submenu: [
                {
                    label: 'حفظ البيانات',
                    accelerator: 'CmdOrCtrl+S',
                    click: () => {
                        mainWindow.webContents.executeJavaScript('saveAllData()');
                    }
                },
                {
                    label: 'استيراد البيانات',
                    click: async () => {
                        const result = await dialog.showOpenDialog(mainWindow, {
                            properties: ['openFile'],
                            filters: [
                                { name: 'JSON Files', extensions: ['json'] },
                                { name: 'All Files', extensions: ['*'] }
                            ]
                        });

                        if (!result.canceled) {
                            const filePath = result.filePaths[0];
                            try {
                                console.log('🔄 محاولة قراءة الملف:', filePath);

                                // التحقق من وجود الملف
                                if (!fs.existsSync(filePath)) {
                                    throw new Error('الملف غير موجود');
                                }

                                // قراءة الملف مع معالجة أفضل للأخطاء
                                let data;
                                try {
                                    data = fs.readFileSync(filePath, 'utf8');
                                    console.log('📄 تم قراءة الملف، حجم البيانات:', data.length, 'حرف');
                                } catch (readError) {
                                    console.error('❌ خطأ في قراءة الملف:', readError);
                                    throw new Error(`فشل في قراءة الملف: ${readError.message}`);
                                }

                                // التحقق من أن الملف ليس فارغاً
                                if (!data || data.trim().length === 0) {
                                    throw new Error('الملف فارغ أو لا يحتوي على بيانات');
                                }

                                // التحقق من صحة البيانات JSON
                                let jsonData;
                                try {
                                    jsonData = JSON.parse(data);
                                    console.log('✅ تم تحليل JSON بنجاح');

                                    // التحقق من وجود البيانات المطلوبة
                                    if (!jsonData || typeof jsonData !== 'object') {
                                        throw new Error('البيانات المستوردة غير صحيحة');
                                    }

                                } catch (jsonError) {
                                    console.error('❌ خطأ في تحليل JSON:', jsonError);
                                    throw new Error(`الملف لا يحتوي على بيانات JSON صحيحة: ${jsonError.message}`);
                                }

                                console.log('✅ تم قراءة الملف بنجاح');

                                // تمرير البيانات باستخدام IPC بدلاً من executeJavaScript
                                mainWindow.webContents.send('import-backup-data', jsonData);

                            } catch (error) {
                                console.error('❌ خطأ في قراءة الملف:', error);
                                dialog.showErrorBox('خطأ في قراءة الملف', `فشل في قراءة الملف: ${error.message}\n\nتأكد من:\n• الملف موجود وغير تالف\n• الملف يحتوي على بيانات JSON صحيحة\n• لديك صلاحية قراءة الملف`);
                            }
                        }
                    }
                },
                {
                    label: 'تصدير البيانات',
                    click: async () => {
                        const result = await dialog.showSaveDialog(mainWindow, {
                            filters: [
                                { name: 'JSON Files', extensions: ['json'] }
                            ],
                            defaultPath: `mangoo-backup-${new Date().toISOString().split('T')[0]}.json`
                        });

                        if (!result.canceled) {
                            try {
                                console.log('🔄 بدء تصدير البيانات...');

                                const data = await mainWindow.webContents.executeJavaScript('exportData()');

                                if (!data) {
                                    throw new Error('لا توجد بيانات للتصدير');
                                }

                                // التحقق من صحة البيانات
                                try {
                                    JSON.parse(data);
                                } catch (jsonError) {
                                    throw new Error('البيانات المُصدرة غير صحيحة');
                                }

                                fs.writeFileSync(result.filePath, data, 'utf8');
                                console.log('✅ تم تصدير البيانات بنجاح إلى:', result.filePath);

                                dialog.showMessageBox(mainWindow, {
                                    type: 'info',
                                    title: 'نجح التصدير',
                                    message: `تم تصدير البيانات بنجاح!\n\nمسار الملف:\n${result.filePath}\n\nيمكنك استخدام هذا الملف لاستعادة البيانات لاحقاً.`
                                });

                            } catch (error) {
                                console.error('❌ خطأ في تصدير البيانات:', error);
                                dialog.showErrorBox('خطأ في التصدير', `فشل في تصدير البيانات: ${error.message}`);
                            }
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'خروج',
                    accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                    click: async () => {
                        console.log('📋 طلب خروج من القائمة');
                        // استخدام نفس منطق تأكيد الحفظ
                        if (mainWindow && !mainWindow.isDestroyed()) {
                            mainWindow.close(); // سيؤدي إلى تشغيل حدث 'close' مع التأكيد
                        } else {
                            console.log('⚠️ النافذة غير موجودة، إغلاق مباشر');
                            isQuitting = true;
                            forceQuit();
                        }
                    }
                }
            ]
        },
        {
            label: 'عرض',
            submenu: [
                {
                    label: 'إعادة تحميل',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        mainWindow.reload();
                    }
                },
                {
                    label: 'ملء الشاشة',
                    accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
                    click: () => {
                        mainWindow.setFullScreen(!mainWindow.isFullScreen());
                    }
                },
                {
                    label: 'أدوات المطور',
                    accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
                    click: () => {
                        mainWindow.webContents.toggleDevTools();
                    }
                }
            ]
        },
        {
            label: 'مساعدة',
            submenu: [
                {
                    label: '🚀 تفعيل البرنامج',
                    click: async () => {
                        try {
                            const result = await showActivationDialog();
                            if (result) {
                                dialog.showMessageBox(mainWindow, {
                                    type: 'info',
                                    title: 'تم التفعيل بنجاح',
                                    message: '🎉 تم تفعيل البرنامج بنجاح!\n\nيمكنك الآن الاستمتاع بجميع ميزات نظام Mangoo بدون قيود.',
                                    buttons: ['موافق']
                                });
                            }
                        } catch (error) {
                            console.error('خطأ في التفعيل:', error);
                            dialog.showErrorBox('خطأ في التفعيل', 'حدث خطأ أثناء محاولة التفعيل');
                        }
                    }
                },
                { type: 'separator' },
                {
                    label: 'إنشاء اختصار سطح المكتب',
                    click: () => {
                        createDesktopShortcut();
                    }
                },
                {
                    label: 'التحقق من اختصار سطح المكتب',
                    click: () => {
                        const shortcutExists = testDesktopShortcut();
                        const message = shortcutExists
                            ? '✅ اختصار سطح المكتب موجود ويعمل بشكل صحيح\n\nيمكنك استخدامه لتشغيل البرنامج من سطح المكتب'
                            : '❌ اختصار سطح المكتب غير موجود\n\nيمكنك إنشاؤه من قائمة مساعدة > إنشاء اختصار سطح المكتب';

                        dialog.showMessageBox(mainWindow, {
                            type: shortcutExists ? 'info' : 'warning',
                            title: 'حالة اختصار سطح المكتب',
                            message: message,
                            buttons: ['موافق']
                        });
                    }
                },
                {
                    label: 'إعادة تعيين التشغيل الأول (لاختبار الاختصار)',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'question',
                            title: 'إعادة تعيين التشغيل الأول',
                            message: 'هل تريد إعادة تعيين حالة التشغيل الأول؟',
                            detail: 'سيتم إنشاء اختصار سطح المكتب مرة أخرى عند إعادة تشغيل البرنامج',
                            buttons: ['نعم', 'إلغاء'],
                            defaultId: 1
                        }).then(result => {
                            if (result.response === 0) {
                                try {
                                    const configPath = path.join(__dirname, 'data', 'app-config.json');
                                    if (fs.existsSync(configPath)) {
                                        const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));
                                        config.firstRunCompleted = false;
                                        fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
                                        console.log('✅ تم إعادة تعيين حالة التشغيل الأول');

                                        dialog.showMessageBox(mainWindow, {
                                            type: 'info',
                                            title: 'تم بنجاح',
                                            message: 'تم إعادة تعيين حالة التشغيل الأول\n\nسيتم إنشاء اختصار سطح المكتب عند إعادة تشغيل البرنامج',
                                            buttons: ['موافق']
                                        });
                                    }
                                } catch (error) {
                                    console.error('خطأ في إعادة تعيين التشغيل الأول:', error);
                                    dialog.showMessageBox(mainWindow, {
                                        type: 'error',
                                        title: 'خطأ',
                                        message: 'فشل في إعادة تعيين التشغيل الأول',
                                        detail: error.message,
                                        buttons: ['موافق']
                                    });
                                }
                            }
                        });
                    }
                },


                { type: 'separator' },
                {
                    label: 'حول التطبيق',
                    click: () => {
                        dialog.showMessageBox(mainWindow, {
                            type: 'info',
                            title: 'حول التطبيق',
                            message: '🥭 Mangoo - نظام إدارة محل العصائر والحلويات الذكي',
                            detail: `النسخة 2.0.0 - إصدار Mangoo المحسن
🥭 نظام إدارة شامل ومتطور لمحلات العصائر والحلويات

👨‍💻 تطوير: فارس نواف
📱 الهاتف: 0569329925
📧 الإيميل: <EMAIL>

🌟 الميزات الرئيسية:
🥭 واجهة Mangoo الحديثة والجذابة
📦 إدارة المخزون الذكية
💰 نظام نقطة البيع المتقدم
🏭 إدارة الوصفات والتصنيع
📊 التقارير والإحصائيات التفاعلية
💾 النسخ الاحتياطي التلقائي
🎨 تصميم احترافي بأيقونة المانجو`
                        });
                    }
                }
            ]
        }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

// التعامل مع أحداث التطبيق
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
    console.log('🔄 جميع النوافذ مغلقة - إنهاء التطبيق');
    isQuitting = true;

    // إعدادات خاصة بـ Windows
    if (process.platform === 'win32') {
        console.log('🖥️ تطبيق إعدادات Windows للإغلاق');
        forceQuit();
    } else {
        app.quit();
    }
});

app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
        createWindow();
    }
});

// معالج للتأكد من الإغلاق الآمن
app.on('before-quit', (event) => {
    console.log('🔄 before-quit event، isQuitting:', isQuitting);
    if (!isQuitting) {
        event.preventDefault();
        console.log('⏸️ منع الإغلاق، سيتم إظهار حوار التأكيد');
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.close();
        } else {
            isQuitting = true;
            forceQuit();
        }
    }
});

// معالج خاص بـ Windows للإغلاق من شريط المهام
if (process.platform === 'win32') {
    app.on('second-instance', () => {
        // إذا تم تشغيل نسخة ثانية، أظهر النافذة الموجودة
        if (mainWindow) {
            if (mainWindow.isMinimized()) mainWindow.restore();
            mainWindow.focus();
        }
    });

    // معالج إغلاق النظام
    app.on('session-end', () => {
        console.log('💻 إنهاء جلسة Windows');
        isQuitting = true;
        forceQuit();
    });
}

// إغلاق فوري عند الطلب من النظام - محسن لـ Windows
process.on('SIGINT', () => {
    console.log('📡 تلقي إشارة SIGINT (Windows)');
    isQuitting = true;
    forceQuit();
});

process.on('SIGTERM', () => {
    console.log('📡 تلقي إشارة SIGTERM (Windows)');
    isQuitting = true;
    forceQuit();
});

// معالجات خاصة بـ Windows
if (process.platform === 'win32') {
    process.on('SIGBREAK', () => {
        console.log('📡 تلقي إشارة SIGBREAK (Windows)');
        isQuitting = true;
        forceQuit();
    });

    // معالج إغلاق النظام
    process.on('exit', (code) => {
        console.log(`🚪 إنهاء العملية برمز: ${code}`);
    });

    // معالج إضافي للتأكد من الإغلاق
    process.on('beforeExit', (code) => {
        console.log(`🔄 قبل الإنهاء برمز: ${code}`);
        if (!isQuitting) {
            isQuitting = true;
            forceQuit();
        }
    });
}

// معالج إضافي للأخطاء غير المتوقعة
process.on('uncaughtException', (error) => {
    console.error('❌ خطأ غير متوقع:', error);
    isQuitting = true;
    forceQuit();
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('❌ رفض غير معالج:', reason);
    // لا نغلق التطبيق هنا، فقط نسجل الخطأ
});

// معالج إضافي لضمان الإغلاق في حالة تعليق النظام
let forceQuitTimer = null;

function setForceQuitTimer() {
    if (forceQuitTimer) {
        clearTimeout(forceQuitTimer);
    }

    forceQuitTimer = setTimeout(() => {
        console.log('⏰ انتهت مهلة الإغلاق الآمن، إغلاق قوي');
        process.exit(0);
    }, 10000); // 10 ثوانٍ كحد أقصى للإغلاق
}

// التعامل مع IPC للتواصل مع العملية المرسلة
ipcMain.handle('save-data', async (event, data) => {
    try {
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir);
        }
        
        const filePath = path.join(dataDir, 'app-data.json');
        fs.writeFileSync(filePath, JSON.stringify(data, null, 2));
        return { success: true };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

ipcMain.handle('load-data', async () => {
    try {
        const filePath = path.join(__dirname, 'data', 'app-data.json');
        if (fs.existsSync(filePath)) {
            const data = fs.readFileSync(filePath, 'utf8');
            return { success: true, data: JSON.parse(data) };
        }
        return { success: true, data: null };
    } catch (error) {
        return { success: false, error: error.message };
    }
});

// التعامل مع طلب الخروج من الواجهة
ipcMain.handle('request-exit', async () => {
    try {
        console.log('📨 تلقي طلب خروج من الواجهة');
        if (mainWindow && !mainWindow.isDestroyed()) {
            mainWindow.close(); // سيؤدي إلى تشغيل حدث 'close' مع التأكيد
            return { success: true };
        } else {
            console.log('⚠️ النافذة غير موجودة، إغلاق مباشر');
            isQuitting = true;
            forceQuit();
            return { success: true };
        }
    } catch (error) {
        console.error('❌ خطأ في طلب الخروج:', error);
        isQuitting = true;
        forceQuit();
        return { success: false, error: error.message };
    }
});

// التعامل مع طلب الخروج المباشر (بدون حوار)
ipcMain.handle('force-exit', async () => {
    try {
        console.log('📨 تلقي طلب خروج مباشر من الواجهة');
        isQuitting = true;
        forceQuit();
        return { success: true };
    } catch (error) {
        console.error('❌ خطأ في الخروج المباشر:', error);
        isQuitting = true;
        forceQuit();
        return { success: false, error: error.message };
    }
});

// التعامل مع طلب الخروج النهائي (أقوى طريقة للخروج)
ipcMain.handle('final-exit', async () => {
    try {
        console.log('🔥 تلقي طلب خروج نهائي من الواجهة');
        isQuitting = true;

        // إغلاق فوري وقوي
        console.log('🔥 بدء الخروج النهائي الفوري');

        // إغلاق جميع النوافذ فوراً
        const allWindows = BrowserWindow.getAllWindows();
        allWindows.forEach(window => {
            if (!window.isDestroyed()) {
                window.removeAllListeners();
                window.destroy();
            }
        });

        // إنهاء فوري للتطبيق
        setTimeout(() => {
            process.exit(0);
        }, 100);

        return { success: true };
    } catch (error) {
        console.error('❌ خطأ في الخروج النهائي:', error);
        // في حالة الخطأ، إنهاء فوري
        process.exit(1);
        return { success: false, error: error.message };
    }
});

// معالجات IPC لنظام التفعيل
ipcMain.handle('check-activation', async () => {
    try {
        console.log('🔍 بدء التحقق من حالة التفعيل...');

        const isActivated = checkActivation();
        const trialExpired = isTrialExpired();
        const remainingDays = getRemainingTrialDays();

        const result = {
            success: true,
            isActivated: isActivated,
            trialExpired: trialExpired,
            remainingDays: remainingDays,
            trialPeriodDays: TRIAL_PERIOD_DAYS
        };

        console.log('✅ نتيجة التحقق من التفعيل:', result);
        return result;
    } catch (error) {
        console.error('❌ خطأ في التحقق من حالة التفعيل:', error);
        return { success: false, error: error.message };
    }
});

ipcMain.handle('show-activation-dialog', async () => {
    try {
        const result = await showActivationDialog();
        return { success: true, activated: result };
    } catch (error) {
        console.error('خطأ في عرض نافذة التفعيل:', error);
        return { success: false, error: error.message };
    }
});

// ===== IPC Handlers لنظام النسخ الاحتياطي والاستيراد المحسن v2.3.0 =====

// معالج إنشاء النسخة الاحتياطية
ipcMain.handle('backup-create', async (event, backupData) => {
    try {
        console.log('🔄 بدء إنشاء النسخة الاحتياطية من main.js...');

        if (backupInProgress) {
            return { success: false, error: 'عملية نسخ احتياطي أخرى قيد التنفيذ' };
        }

        backupInProgress = true;

        // التحقق من صحة البيانات
        if (!backupData || !backupData.data) {
            throw new Error('بيانات النسخة الاحتياطية غير صحيحة');
        }

        // إنشاء مجلد النسخ الاحتياطية إذا لم يكن موجوداً
        const backupsDir = path.join(__dirname, 'data', 'backups');
        if (!fs.existsSync(backupsDir)) {
            fs.mkdirSync(backupsDir, { recursive: true });
        }

        // تسمية الملف
        const now = new Date();
        const dateStr = now.toISOString().split('T')[0];
        const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
        const fileName = `Mangoo_Backup_${dateStr}_${timeStr}.json`;
        const filePath = path.join(backupsDir, fileName);

        // حفظ النسخة الاحتياطية
        fs.writeFileSync(filePath, JSON.stringify(backupData, null, 2), 'utf8');

        console.log('✅ تم حفظ النسخة الاحتياطية في:', filePath);

        backupInProgress = false;
        return {
            success: true,
            filePath: filePath,
            fileName: fileName,
            fileSize: fs.statSync(filePath).size
        };

    } catch (error) {
        console.error('❌ خطأ في إنشاء النسخة الاحتياطية:', error);
        backupInProgress = false;
        return { success: false, error: error.message };
    }
});

// معالج استعادة النسخة الاحتياطية
ipcMain.handle('backup-restore', async (event, filePath) => {
    try {
        console.log('🔄 بدء استعادة النسخة الاحتياطية من main.js...');

        if (restoreInProgress) {
            return { success: false, error: 'عملية استعادة أخرى قيد التنفيذ' };
        }

        restoreInProgress = true;

        // التحقق من وجود الملف
        if (!fs.existsSync(filePath)) {
            throw new Error('ملف النسخة الاحتياطية غير موجود');
        }

        // قراءة الملف
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const backupData = JSON.parse(fileContent);

        // التحقق من صحة البيانات
        if (!backupData || !backupData.data) {
            throw new Error('ملف النسخة الاحتياطية تالف أو غير صحيح');
        }

        console.log('✅ تم قراءة النسخة الاحتياطية بنجاح');

        restoreInProgress = false;
        return {
            success: true,
            data: backupData,
            fileSize: fs.statSync(filePath).size
        };

    } catch (error) {
        console.error('❌ خطأ في استعادة النسخة الاحتياطية:', error);
        restoreInProgress = false;
        return { success: false, error: error.message };
    }
});

// معالج فحص سلامة النسخة الاحتياطية
ipcMain.handle('backup-validate', async (event, filePath) => {
    try {
        console.log('🔍 فحص سلامة النسخة الاحتياطية:', filePath);

        if (!fs.existsSync(filePath)) {
            return { success: false, error: 'الملف غير موجود' };
        }

        const fileContent = fs.readFileSync(filePath, 'utf8');
        const backupData = JSON.parse(fileContent);

        // فحص أساسي للبنية
        const validation = {
            hasMetadata: !!backupData.metadata,
            hasData: !!backupData.data,
            hasStatistics: !!backupData.statistics,
            fileSize: fs.statSync(filePath).size,
            version: backupData.metadata?.version || 'غير محدد',
            createdAt: backupData.metadata?.createdAt || 'غير محدد'
        };

        return { success: true, validation: validation };

    } catch (error) {
        console.error('❌ خطأ في فحص النسخة الاحتياطية:', error);
        return { success: false, error: error.message };
    }
});

// معالج الحصول على قائمة النسخ الاحتياطية المحفوظة
ipcMain.handle('backup-list', async () => {
    try {
        const backupsDir = path.join(__dirname, 'data', 'backups');

        if (!fs.existsSync(backupsDir)) {
            return { success: true, backups: [] };
        }

        const files = fs.readdirSync(backupsDir)
            .filter(file => file.endsWith('.json'))
            .map(file => {
                const filePath = path.join(backupsDir, file);
                const stats = fs.statSync(filePath);

                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const data = JSON.parse(content);

                    return {
                        fileName: file,
                        filePath: filePath,
                        size: stats.size,
                        createdAt: stats.birthtime,
                        modifiedAt: stats.mtime,
                        version: data.metadata?.version || 'غير محدد',
                        type: data.metadata?.type || 'غير محدد'
                    };
                } catch (error) {
                    return {
                        fileName: file,
                        filePath: filePath,
                        size: stats.size,
                        createdAt: stats.birthtime,
                        modifiedAt: stats.mtime,
                        version: 'خطأ في القراءة',
                        type: 'ملف تالف'
                    };
                }
            })
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        return { success: true, backups: files };

    } catch (error) {
        console.error('❌ خطأ في الحصول على قائمة النسخ الاحتياطية:', error);
        return { success: false, error: error.message };
    }
});

// معالج حذف النسخة الاحتياطية
ipcMain.handle('backup-delete', async (event, filePath) => {
    try {
        if (!fs.existsSync(filePath)) {
            return { success: false, error: 'الملف غير موجود' };
        }

        fs.unlinkSync(filePath);
        console.log('✅ تم حذف النسخة الاحتياطية:', filePath);

        return { success: true };

    } catch (error) {
        console.error('❌ خطأ في حذف النسخة الاحتياطية:', error);
        return { success: false, error: error.message };
    }
});

// معالج تصدير النسخة الاحتياطية
ipcMain.handle('backup-export', async (event, backupData, fileName) => {
    try {
        const result = await dialog.showSaveDialog(mainWindow, {
            title: 'حفظ النسخة الاحتياطية',
            defaultPath: fileName || 'Mangoo_Backup.json',
            filters: [
                { name: 'ملفات JSON', extensions: ['json'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ]
        });

        if (result.canceled) {
            return { success: false, error: 'تم إلغاء العملية' };
        }

        fs.writeFileSync(result.filePath, JSON.stringify(backupData, null, 2), 'utf8');

        return {
            success: true,
            filePath: result.filePath,
            fileSize: fs.statSync(result.filePath).size
        };

    } catch (error) {
        console.error('❌ خطأ في تصدير النسخة الاحتياطية:', error);
        return { success: false, error: error.message };
    }
});

// معالج استيراد النسخة الاحتياطية
ipcMain.handle('backup-import', async () => {
    try {
        const result = await dialog.showOpenDialog(mainWindow, {
            title: 'اختيار ملف النسخة الاحتياطية',
            filters: [
                { name: 'ملفات JSON', extensions: ['json'] },
                { name: 'جميع الملفات', extensions: ['*'] }
            ],
            properties: ['openFile']
        });

        if (result.canceled || !result.filePaths.length) {
            return { success: false, error: 'تم إلغاء العملية' };
        }

        const filePath = result.filePaths[0];
        const fileContent = fs.readFileSync(filePath, 'utf8');
        const backupData = JSON.parse(fileContent);

        return {
            success: true,
            data: backupData,
            filePath: filePath,
            fileName: path.basename(filePath),
            fileSize: fs.statSync(filePath).size
        };

    } catch (error) {
        console.error('❌ خطأ في استيراد النسخة الاحتياطية:', error);
        return { success: false, error: error.message };
    }
});

console.log('✅ تم تحميل IPC handlers لنظام النسخ الاحتياطي والاستيراد المحسن v2.3.0');

