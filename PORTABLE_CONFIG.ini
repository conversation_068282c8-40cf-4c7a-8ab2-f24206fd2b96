[PORTABLE_SETTINGS]
; Mangoo System v2.3.0 Portable Configuration
; This file ensures the application runs in portable mode

[APPLICATION]
Name=Mangoo System
Version=2.3.0
Type=Portable
RequiresAdmin=false
RequiresInstallation=false

[COMPATIBILITY]
Windows7=true
Windows8=true
Windows10=true
Windows11=true
Architecture=x64
MinimumRAM=2GB
RecommendedRAM=4GB

[PORTABLE_FEATURES]
DataDirectory=PortableData
UserDataDirectory=PortableData\UserData
CacheDirectory=PortableData\Cache
TempDirectory=PortableData\Temp
LogFile=PortableData\app.log
SettingsFile=PortableData\settings.json
DatabaseFile=PortableData\database.db

[SECURITY]
DisableGPU=true
DisableSandbox=true
DisableWebSecurity=true
NoRegistryChanges=true
NoSystemFileChanges=true
SelfContained=true

[STARTUP_FLAGS]
Flag1=--disable-gpu
Flag2=--disable-gpu-sandbox
Flag3=--disable-software-rasterizer
Flag4=--no-sandbox
Flag5=--disable-web-security
Flag6=--disable-features=VizDisplayCompositor
Flag7=--disable-background-timer-throttling
Flag8=--disable-backgrounding-occluded-windows
Flag9=--disable-renderer-backgrounding
Flag10=--disable-field-trial-config

[TROUBLESHOOTING]
; Common issues and solutions
Issue1=If app doesn't start, run as administrator
Issue2=If GPU errors occur, flags above should resolve them
Issue3=If printing issues occur, check Windows print spooler service
Issue4=If fonts look wrong, ensure all .pak files are present
Issue5=If data is lost, check PortableData folder

[SUPPORT]
Developer=Fares Nawaf
Phone=**********
Email=<EMAIL>
WhatsApp=**********

[BACKUP_INSTRUCTIONS]
Step1=Copy entire Mangoo_System_v2.3.0_SIMPLIFIED_BACKUP folder
Step2=Paste to USB drive or cloud storage
Step3=Run from any Windows PC without installation
Step4=All data preserved in PortableData subfolder

[REQUIRED_FILES]
; These files must be present for portable operation
File1=mangoo-juice-shop-management.exe
File2=resources\app\*
File3=locales\*
File4=*.dll
File5=*.pak
File6=*.bin
File7=*.dat
File8=version
File9=LICENSE*

[OPTIONAL_FILES]
; Essential files for portable experience
File1=START_MANGOO_WORKING.bat
File2=PORTABLE_CONFIG.ini
File3=README.txt

[DESKTOP_SHORTCUT]
; Desktop shortcut creation settings
AutoCreateOnFirstRun=true
ShortcutName=Mangoo System
ShortcutDescription=نظام إدارة محل العصائر والحلويات الذكي
TargetFile=START_MANGOO_WORKING.bat
IconFile=resources\app\assets\mangoo-icon.ico
WorkingDirectory=.
WindowStyle=7
SilentOperation=true
SelfContained=true
